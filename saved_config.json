{"name": "Untitled Workflow", "description": "Untitled_Workflow", "workflow_data": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"MCP_MetaAds_create_adset-*************_campaign_id": {"node_id": "MCP_MetaAds_create_adset-*************", "node_name": "MetaAds - create_adset", "input_name": "campaign_id", "connected_to_start": true, "required": true, "input_type": "string", "options": null}}}}, "width": 208, "height": 141, "selected": false, "dragging": false}, {"id": "MCP_MetaAds_create_adset-*************", "type": "WorkflowNode", "position": {"x": 520, "y": -160}, "data": {"label": "MetaAds - create_adset", "type": "mcp", "originalType": "MCP_MetaAds_create_adset", "definition": {"name": "MCP_MetaAds_create_adset", "display_name": "MetaAds - create_adset", "description": "Create a new ad set in a Meta Ads account", "category": "Tools", "icon": "Cloud", "beta": true, "inputs": [{"name": "campaign_id", "display_name": "Campaign Id", "info": "Meta Ads campaign ID this ad set belongs to", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "name", "display_name": "Name", "info": "Ad set name", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "status", "display_name": "Status", "info": "Initial ad set status", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "PAUSED", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "daily_budget", "display_name": "Daily Budget", "info": "Daily budget in cents", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "lifetime_budget", "display_name": "Lifetime Budget", "info": "Lifetime budget in cents", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "targeting", "display_name": "Targeting", "info": "Targeting specifications (e.g., age, location, interests)", "input_type": "object", "input_types": ["object", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "optimization_goal", "display_name": "Optimization Goal", "info": "Conversion optimization goal (e.g., 'LINK_CLICKS')", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "billing_event", "display_name": "Billing Event", "info": "How you're charged (e.g., 'IMPRESSIONS')", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "bid_amount", "display_name": "<PERSON><PERSON>", "info": "Bid amount in account currency (in cents)", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "bid_strategy", "display_name": "Bid Strategy", "info": "Bid strategy (e.g., 'LOWEST_COST')", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "start_time", "display_name": "Start Time", "info": "Start time (ISO 8601)", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "end_time", "display_name": "End Time", "info": "End time (ISO 8601)", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "is_valid": true, "path": "mcp.mcp_metaads_create_adset", "type": "MCP", "env_keys": [], "env_credential_status": "pending_input", "logo": null, "oauth_details": null, "mcp_info": {"server_id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "server_path": "", "tool_name": "create_adset", "input_schema": {"description": "<PERSON><PERSON><PERSON> for creating an ad set", "properties": {"campaign_id": {"description": "Meta Ads campaign ID this ad set belongs to", "title": "Campaign Id", "type": "string"}, "name": {"description": "Ad set name", "title": "Name", "type": "string"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "PAUSED", "description": "Initial ad set status", "title": "Status"}, "daily_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Daily budget in cents", "title": "Daily Budget"}, "lifetime_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Lifetime budget in cents", "title": "Lifetime Budget"}, "targeting": {"additionalProperties": true, "description": "Targeting specifications (e.g., age, location, interests)", "title": "Targeting", "type": "object"}, "optimization_goal": {"description": "Conversion optimization goal (e.g., 'LINK_CLICKS')", "title": "Optimization Goal", "type": "string"}, "billing_event": {"description": "How you're charged (e.g., 'IMPRESSIONS')", "title": "Billing Event", "type": "string"}, "bid_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Bid amount in account currency (in cents)", "title": "<PERSON><PERSON>"}, "bid_strategy": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Bid strategy (e.g., 'LOWEST_COST')", "title": "Bid Strategy"}, "start_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Start time (ISO 8601)", "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "End time (ISO 8601)", "title": "End Time"}}, "required": ["campaign_id", "name", "targeting", "optimization_goal", "billing_event"], "title": "CreateAdSetRequest", "type": "object"}, "output_schema": {"content": [{"type": "text", "text": "{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"120227497234150727\"\n  }\n}"}], "isError": false}}}, "config": {"status": "PAUSED", "name": "Solar Power Bank", "daily_budget": "1000", "lifetime_budget": "", "targeting": {"geo_locations": {"countries": ["US"]}, "publisher_platforms": ["facebook"], "facebook_positions": ["feed"]}, "optimization_goal": "LINK_CLICKS", "billing_event": "IMPRESSIONS", "bid_strategy": "LOWEST_COST_WITHOUT_CAP"}}, "style": {"opacity": 1}, "width": 208, "height": 526, "selected": true, "positionAbsolute": {"x": 520, "y": -160}, "dragging": false}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "start-node", "sourceHandle": "flow", "target": "MCP_MetaAds_create_adset-*************", "targetHandle": "campaign_id", "type": "default", "id": "reactflow__edge-start-nodeflow-MCP_MetaAds_create_adset-*************campaign_id"}]}, "start_node_data": [{"field": "campaign_id", "type": "string", "transition_id": "transition-MCP_MetaAds_create_adset-*************"}]}