# app/services/marketplace_functions.py
import grpc
import json
import structlog
from datetime import datetime, timezone
from sqlalchemy.orm import Session , joinedload
from app.db.session import SessionLocal
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.models.workflow_rating import WorkflowRating
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.utils.constants.constants import WorkflowOwnerTypeEnum, WorkflowStatusEnum, WorkflowVisibilityEnum
from app.utils.kafka.kafka_service import KafkaProducer
from app.helpers.workflow_to_protobuf import (
    _marketplace_listing_to_protobuf,
    _listing_to_marketplace_workflow,
    _workflow_to_protobuf,
)
from app.services.workflow_auth_analyzer import get_workflow_auth_summary

# Initialize structured logger
logger = structlog.get_logger()


class WorkflowMarketplaceFunctions(workflow_pb2_grpc.WorkflowServiceServicer):
    def __init__(self):
        """Initialize the WorkflowService with a KafkaProducer instance"""
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def getTemplate(
        self, request: workflow_pb2.GetTemplateRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetTemplateResponse:
        """
        Retrieve a marketplace listing by its ID (backward compatibility for template API).

        Args:
            request: The request containing the template ID (marketplace listing ID).
            context: The gRPC context for handling the request.

        Returns:
            Response containing the marketplace listing details if found.
        """
        db = self.get_db()
        logger.info("get_marketplace_listing_request", listing_id=request.id)
        try:
            # Get marketplace listing instead of template
            marketplace_listing = (
                db.query(WorkflowMarketplaceListing)
                .filter(WorkflowMarketplaceListing.id == request.id)
                .first()
            )
            if marketplace_listing is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Marketplace listing not found")
                return workflow_pb2.GetTemplateResponse(
                    success=False, message="Marketplace listing not found"
                )

            logger.info("marketplace_listing_retrieved", listing_id=marketplace_listing.id)

            # Check if user_id is provided and if the user has already used this marketplace listing
            is_added = False
            has_updates = False
            latest_version_id = ""
            user_source_version_id = ""
            
            if request.HasField("user_id"):
                # Check if there are any workflows created from this marketplace listing by the user
                user_workflow = (
                    db.query(Workflow)
                    .filter(
                        Workflow.workflow_template_id == marketplace_listing.workflow_id,
                        Workflow.owner_id == request.user_id,
                    )
                    .first()
                )
                is_added = user_workflow is not None
                
                # If user has cloned this workflow, check for updates
                if is_added and user_workflow.source_version_id:
                    user_source_version_id = user_workflow.source_version_id
                    
                    # Get the latest published version of the source workflow
                    source_workflow = (
                        db.query(Workflow)
                        .filter(Workflow.id == marketplace_listing.workflow_id)
                        .first()
                    )
                    
                    if source_workflow and source_workflow.current_version_id:
                        latest_version_id = source_workflow.current_version_id
                        # Check if the user's cloned workflow is based on an older version
                        has_updates = user_workflow.source_version_id != source_workflow.current_version_id
                        
                        logger.info(
                            "checking_for_updates",
                            user_id=request.user_id,
                            user_workflow_id=user_workflow.id,
                            user_source_version_id=user_source_version_id,
                            latest_version_id=latest_version_id,
                            has_updates=has_updates,
                        )
                
                logger.info(
                    "checking_if_user_used_marketplace_listing",
                    user_id=request.user_id,
                    listing_id=marketplace_listing.id,
                    is_added=is_added,
                    has_updates=has_updates,
                )

            # Convert marketplace listing to protobuf template format for backward compatibility
            template_proto = _marketplace_listing_to_protobuf(marketplace_listing)

            # Override the version sync fields with user-specific information
            template_proto.is_added = is_added
            template_proto.has_updates = has_updates
            
            # Set the correct version IDs based on user context
            if latest_version_id:
                template_proto.current_version_id = latest_version_id
            if user_source_version_id:
                template_proto.source_version_id = user_source_version_id
            
            # Legacy support for fields that might not exist in protobuf
            if hasattr(template_proto, 'latest_version_id'):
                template_proto.latest_version_id = latest_version_id

            print(f"[DEBUG] Marketplace listing retrieved: {template_proto}")
            return workflow_pb2.GetTemplateResponse(
                success=True,
                message=f"Marketplace listing {marketplace_listing.title} retrieved successfully",
                template=template_proto,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=e)
            return workflow_pb2.GetTemplateResponse(success=False, message="Internal Server Error")
        finally:
            db.close()

    def getMarketplaceWorkflows(
        self, request: workflow_pb2.GetMarketplaceWorkflowsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetMarketplaceWorkflowsResponse:
        """
        Retrieves a paginated list of public workflow templates for the marketplace.

        Args:
            request: The request containing pagination, search, and filter parameters
            context: The gRPC context for handling errors

        Returns:
            Response containing the list of marketplace workflows and pagination metadata
        """
        db = self.get_db()
        logger.info(
            "get_marketplace_workflows_request",
            request=request,
        )

        try:
            # Start with a base query for public marketplace listings
            query = db.query(WorkflowMarketplaceListing).filter(
                WorkflowMarketplaceListing.visibility == WorkflowVisibilityEnum.PUBLIC,
                WorkflowMarketplaceListing.status == WorkflowStatusEnum.ACTIVE,
            )

            # Apply search filter if provided
            if request.HasField("search") and request.search:
                search_term = f"%{request.search}%"
                query = query.filter(
                    (WorkflowMarketplaceListing.title.ilike(search_term))
                    | (WorkflowMarketplaceListing.description.ilike(search_term))
                )

            # Apply category filter if provided
            if request.HasField("category") and request.category:
                category_value = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                query = query.filter(WorkflowMarketplaceListing.category == category_value)

            # Apply tags filter if provided
            if request.tags:
                # Filter by tags (exact match for any tag in the list)
                query = query.filter(WorkflowMarketplaceListing.tags.contains(request.tags))

            # Apply sorting
            if request.HasField("sort_by") and request.sort_by:
                sort_by = request.sort_by
                if sort_by == "NEWEST":
                    query = query.order_by(WorkflowMarketplaceListing.created_at.desc())
                elif sort_by == "OLDEST":
                    query = query.order_by(WorkflowMarketplaceListing.created_at.asc())
                elif sort_by == "MOST_POPULAR":
                    query = query.order_by(WorkflowMarketplaceListing.use_count.desc())
                elif sort_by == "HIGHEST_RATED":
                    query = query.order_by(WorkflowMarketplaceListing.average_rating.desc())
                else:
                    # Default to newest
                    query = query.order_by(WorkflowMarketplaceListing.created_at.desc())
            else:
                # Default sorting by newest
                query = query.order_by(WorkflowMarketplaceListing.created_at.desc())

            # Get total count with filters applied
            total = query.count()

            # Apply pagination
            page = request.page if request.page > 0 else 1
            page_size = min(request.page_size, 100) if request.page_size > 0 else 10

            listings = query.offset((page - 1) * page_size).limit(page_size).all()

            # Calculate pagination metadata
            total_pages = (total + page_size - 1) // page_size if total > 0 else 1
            has_next = page < total_pages
            has_prev = page > 1

            # Convert marketplace listings to protobuf format
            listing_list = [_listing_to_marketplace_workflow(listing) for listing in listings]

            logger.info(
                "marketplace_workflows_retrieved",
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
            )

            return workflow_pb2.GetMarketplaceWorkflowsResponse(
                success=True,
                message="Marketplace workflows retrieved successfully",
                workflows=listing_list,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
                has_next=has_next,
                has_prev=has_prev,
                next_page=page + 1 if has_next else 0,
                prev_page=page - 1 if has_prev else 0,
            )

        except Exception as e:
            logger.error("get_marketplace_workflows_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to retrieve marketplace workflows: {str(e)}")
            return workflow_pb2.GetMarketplaceWorkflowsResponse(
                success=False,
                message=f"Failed to retrieve marketplace workflows: {str(e)}",
                workflows=[],
                total=0,
                page=request.page,
                page_size=request.page_size,
                total_pages=0,
                has_next=False,
                has_prev=False,
            )
        finally:
            db.close()

    def rateWorkflow(
        self, request: workflow_pb2.RateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.RateWorkflowResponse:
        """
        Rate a workflow and update its average rating.

        Args:
            request: Contains the workflow ID, user ID, and rating value
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated average rating
        """
        db = self.get_db()
        try:
            logger.info(
                "rate_workflow_request",
                workflow_id=request.workflow_id,
                user_id=request.user_id,
                rating=request.rating,
            )

            # Validate rating value (between 1.0 and 5.0)
            if request.rating < 1.0 or request.rating > 5.0:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Rating must be between 1.0 and 5.0")
                return workflow_pb2.RateWorkflowResponse(
                    success=False, message="Rating must be between 1.0 and 5.0", average_rating=0.0
                )

            # Check if workflow exists
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found")
                return workflow_pb2.RateWorkflowResponse(
                    success=False,
                    message=f"Workflow with ID {request.workflow_id} not found",
                    average_rating=0.0,
                )

            # Check if user has already rated this workflow
            existing_rating = (
                db.query(WorkflowRating)
                .filter(
                    WorkflowRating.workflow_id == request.workflow_id,
                    WorkflowRating.user_id == request.user_id,
                )
                .first()
            )

            if existing_rating:
                # Update existing rating
                old_rating = existing_rating.rating
                existing_rating.rating = request.rating
                existing_rating.updated_at = datetime.now(timezone.utc)
                db.commit()
                logger.info(
                    "updated_workflow_rating",
                    workflow_id=request.workflow_id,
                    user_id=request.user_id,
                    old_rating=old_rating,
                    new_rating=request.rating,
                )
            else:
                # Create new rating
                new_rating = WorkflowRating(
                    workflow_id=request.workflow_id, user_id=request.user_id, rating=request.rating
                )
                db.add(new_rating)
                db.commit()
                logger.info(
                    "created_workflow_rating",
                    workflow_id=request.workflow_id,
                    user_id=request.user_id,
                    rating=request.rating,
                )

            # Calculate new average rating
            ratings = (
                db.query(WorkflowRating)
                .filter(WorkflowRating.workflow_id == request.workflow_id)
                .all()
            )
            total_rating = sum(r.rating for r in ratings)
            average_rating = total_rating / len(ratings) if ratings else 0.0

            # Update workflow with new average rating
            workflow.average_rating = average_rating
            workflow.updated_at = datetime.now(timezone.utc)
            db.commit()

            # If this workflow was created from a source workflow, update marketplace listings for that source
            if workflow.workflow_template_id:
                # Find marketplace listings for the source workflow
                marketplace_listings = (
                    db.query(WorkflowMarketplaceListing)
                    .filter(WorkflowMarketplaceListing.workflow_id == workflow.workflow_template_id)
                    .all()
                )

                for marketplace_listing in marketplace_listings:
                    # Get all ratings for all workflows created from this source workflow
                    listing_workflows = (
                        db.query(Workflow)
                        .filter(Workflow.workflow_template_id == workflow.workflow_template_id)
                        .all()
                    )

                    listing_workflow_ids = [w.id for w in listing_workflows]

                    if listing_workflow_ids:
                        listing_ratings = (
                            db.query(WorkflowRating)
                            .filter(WorkflowRating.workflow_id.in_(listing_workflow_ids))
                            .all()
                        )

                        if listing_ratings:
                            listing_total_rating = sum(r.rating for r in listing_ratings)
                            listing_average_rating = listing_total_rating / len(listing_ratings)

                            marketplace_listing.average_rating = listing_average_rating
                            marketplace_listing.updated_at = datetime.now(timezone.utc)
                            db.commit()

                            logger.info(
                                "updated_marketplace_listing_rating",
                                listing_id=marketplace_listing.id,
                                average_rating=listing_average_rating,
                            )

            return workflow_pb2.RateWorkflowResponse(
                success=True,
                message=f"Rating for workflow {workflow.name} updated successfully",
                average_rating=average_rating,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error rating workflow: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.RateWorkflowResponse(
                success=False, message="Failed to update workflow rating", average_rating=0.0
            )
        finally:
            db.close()

    def useWorkflow(
        self, request: workflow_pb2.UseWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UseWorkflowResponse:
        """
        Create a copy of a marketplace workflow for a user and increment the listing's use count.
        The request.workflow_id can be either a WorkflowMarketplaceListing.id or a Workflow.id
        (if the latter, it will try to use its current, active, public marketplace listing).

        Args:
            request: Contains the ID (workflow_id or marketplace_listing_id) and user ID
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated use count
        """
        db = self.get_db()
        try:
            logger.info(
                "use_workflow_request_received",
                input_id=request.workflow_id,
                user_id=request.user_id,
            )

            marketplace_listing = None
            source_workflow_for_template_details = (
                None  # This will be the original workflow being cloned
            )

            # Attempt 1: Treat request.workflow_id as WorkflowMarketplaceListing.id
            # Eager load related objects to reduce further queries
            listing_by_id = (
                db.query(WorkflowMarketplaceListing)
                .options(
                    joinedload(WorkflowMarketplaceListing.workflow_version),
                    joinedload(WorkflowMarketplaceListing.workflow),  # This is the source_workflow
                )
                .filter(
                    WorkflowMarketplaceListing.id == request.workflow_id,
                    WorkflowMarketplaceListing.visibility == WorkflowVisibilityEnum.PUBLIC,
                    WorkflowMarketplaceListing.status == WorkflowStatusEnum.ACTIVE,
                )
                .first()
            )

            if listing_by_id:
                marketplace_listing = listing_by_id
                source_workflow_for_template_details = marketplace_listing.workflow
                if not source_workflow_for_template_details:
                    # This should ideally not happen if data integrity is maintained
                    # and workflow relationship on listing is not nullable
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(
                        f"Marketplace listing {request.workflow_id} is orphaned (missing parent workflow)."
                    )
                    return workflow_pb2.UseWorkflowResponse(
                        success=False, message="Internal error: Orphaned marketplace listing."
                    )
            else:
                # Attempt 2: Treat request.workflow_id as Workflow.id
                # Find the workflow, then find an active, public listing for its current_version_id
                potential_source_workflow = (
                    db.query(Workflow)
                    .options(
                        joinedload(Workflow.current_version)  # Eager load current_version
                        # We'll query its listings separately or rely on backref if already loaded
                    )
                    .filter(Workflow.id == request.workflow_id)
                    .first()
                )

                if potential_source_workflow and potential_source_workflow.current_version_id:
                    # Now find an active, public listing associated with this workflow's current version
                    listing_for_workflow = (
                        db.query(WorkflowMarketplaceListing)
                        .options(
                            joinedload(WorkflowMarketplaceListing.workflow_version),
                            # The 'workflow' relationship on listing will point back to potential_source_workflow
                        )
                        .filter(
                            WorkflowMarketplaceListing.workflow_id == potential_source_workflow.id,
                            WorkflowMarketplaceListing.workflow_version_id
                            == potential_source_workflow.current_version_id,
                            WorkflowMarketplaceListing.visibility == WorkflowVisibilityEnum.PUBLIC,
                            WorkflowMarketplaceListing.status == WorkflowStatusEnum.ACTIVE,
                        )
                        .first()
                    )
                    if listing_for_workflow:
                        marketplace_listing = listing_for_workflow
                        source_workflow_for_template_details = (
                            potential_source_workflow  # The workflow we looked up by ID
                        )
                    # If listing_for_workflow is None, marketplace_listing remains None
                # If potential_source_workflow is None, marketplace_listing remains None

            # Check if a usable marketplace_listing was found by either method
            if not marketplace_listing or not source_workflow_for_template_details:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                details = f"No active, public marketplace item found for ID '{request.workflow_id}' (interpreted as listing ID or source workflow ID)."
                context.set_details(details)
                return workflow_pb2.UseWorkflowResponse(
                    success=False,
                    message=details,
                    use_count=0,
                )

            # At this point, marketplace_listing is valid and public/active.
            # source_workflow_for_template_details is the original Workflow object that this listing pertains to.

            # Check if the user has already cloned this workflow
            existing_workflow = (
                db.query(Workflow)
                .filter(
                    Workflow.workflow_template_id == source_workflow_for_template_details.id,
                    Workflow.owner_id == request.user_id,
                    # Consider adding a check for active status if necessary,
                    # but the request implies wanting to know if *any* clone exists.
                )
                .first()
            )

            if existing_workflow:
                logger.info(
                    "user_already_has_workflow",
                    user_id=request.user_id,
                    source_workflow_id=source_workflow_for_template_details.id,
                    existing_workflow_id=existing_workflow.id,
                )
                return workflow_pb2.UseWorkflowResponse(
                    success=False,
                    message=f"You already have this workflow ('{existing_workflow.name}') in your workspace.",
                    workflow_id=existing_workflow.id, # Include the existing workflow's ID
                    use_count=marketplace_listing.use_count, # Return current use count
                )

            # If the user does not have it, proceed with cloning
            logger.info(
                "cloning_new_workflow_for_user",
                user_id=request.user_id,
                source_workflow_id=source_workflow_for_template_details.id,
                marketplace_listing_id=marketplace_listing.id,
            )

            # workflow_version_to_clone is the specific version being listed.
            workflow_version_to_clone = marketplace_listing.workflow_version
            if not workflow_version_to_clone:
                # This should not happen if the joinedload worked or if data integrity is maintained
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(
                    f"Workflow version for marketplace listing {marketplace_listing.id} not found."
                )
                return workflow_pb2.UseWorkflowResponse(
                    success=False,
                    message="Internal error: Workflow version for marketplace listing not found.",
                    workflow_id="", # No workflow created
                    use_count=0,
                )

            # Determine if the cloned workflow should be customizable
            # This comes from the original source workflow, not the version or listing
            is_customizable_for_clone = source_workflow_for_template_details.is_customizable

            # Create a new workflow based on the identified marketplace listing and its version
            new_workflow = Workflow(
                name=marketplace_listing.title,  # Or workflow_version_to_clone.name
                description=marketplace_listing.description,  # Or workflow_version_to_clone.description
                image_url=workflow_version_to_clone.image_url,  # Prefer version's image
                workflow_url=workflow_version_to_clone.workflow_url,
                builder_url=workflow_version_to_clone.builder_url,
                start_nodes=(
                    workflow_version_to_clone.start_nodes
                    if workflow_version_to_clone.start_nodes
                    else []
                ),
                available_nodes=(
                    workflow_version_to_clone.available_nodes
                    if workflow_version_to_clone.available_nodes
                    else []
                ),
                owner_id=request.user_id,
                user_ids=[request.user_id],
                owner_type=WorkflowOwnerTypeEnum.USER,  # Explicitly use enum member
                # The workflow_template_id is the ID of the original workflow that was listed
                workflow_template_id=source_workflow_for_template_details.id,
                # The template_owner_id is the owner of that original workflow
                template_owner_id=source_workflow_for_template_details.owner_id,
                # Track which version this workflow was cloned from for update detection
                source_version_id=workflow_version_to_clone.id,
                is_imported=True,
                visibility=WorkflowVisibilityEnum.PRIVATE,
                category=marketplace_listing.category,  # Or workflow_version_to_clone.category
                tags=(
                    marketplace_listing.tags if marketplace_listing.tags else []
                ),  # Or workflow_version_to_clone.tags
                is_updated=False,
                is_customizable=is_customizable_for_clone,
                status=WorkflowStatusEnum.ACTIVE,
                # created_at, updated_at will be set by default
            )

            db.add(new_workflow)
            db.flush()  # Get the new_workflow.id

            # Create v1 version for the new workflow
            v1_version = WorkflowVersion(
                workflow_id=new_workflow.id,
                version_number="1.0.0",
                name=new_workflow.name,  # Use the new workflow's name
                description=new_workflow.description,  # Use the new workflow's description
                image_url=new_workflow.image_url,
                workflow_url=new_workflow.workflow_url,  # Copied from workflow_version_to_clone
                builder_url=new_workflow.builder_url,  # Copied from workflow_version_to_clone
                start_nodes=new_workflow.start_nodes,  # Copied from workflow_version_to_clone
                available_nodes=new_workflow.available_nodes,  # Copied from workflow_version_to_clone
                category=new_workflow.category,  # Copied from marketplace_listing/version
                tags=new_workflow.tags,  # Copied from marketplace_listing/version
                changelog="Initial version created from marketplace.",
                is_customizable=is_customizable_for_clone,  # Same as parent workflow
                status=WorkflowStatusEnum.ACTIVE,
                # created_at will be set by default
            )

            db.add(v1_version)
            db.flush()  # Get the v1_version.id

            new_workflow.current_version_id = v1_version.id
            # db.add(new_workflow) # Already added, SQLAlchemy tracks changes

            # Increment the marketplace listing's use count
            marketplace_listing.use_count += 1
            marketplace_listing.updated_at = datetime.now(
                timezone.utc
            )  # Use timezone-aware datetime
            # db.add(marketplace_listing) # Already in session, SQLAlchemy tracks changes

            db.commit()
            db.refresh(new_workflow)  # Refresh to get committed state and relationships
            db.refresh(marketplace_listing)
            # db.refresh(v1_version) # If needed

            logger.info(
                "workflow_created_from_marketplace_item",
                new_workflow_id=new_workflow.id,
                cloned_from_workflow_id=source_workflow_for_template_details.id,
                cloned_from_version_id=workflow_version_to_clone.id,
                marketplace_listing_id=marketplace_listing.id,
                user_id=request.user_id,
                new_use_count=marketplace_listing.use_count,
            )

            return workflow_pb2.UseWorkflowResponse(
                success=True,
                message=f"New workflow '{new_workflow.name}' created from marketplace item '{marketplace_listing.title}'.",
                use_count=marketplace_listing.use_count,
                workflow_id=new_workflow.id,  # Return the ID of the newly created workflow
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error using marketplace item: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.UseWorkflowResponse(
                success=False,
                message="Failed to create workflow from marketplace item.",
                use_count=0,
            )
        finally:
            db.close()

    def pullUpdatesFromSource(
        self, request: workflow_pb2.PullUpdatesFromSourceRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.PullUpdatesFromSourceResponse:
        """
        Pull updates from the source workflow for a cloned workflow.

        Enhanced to create a new version with source changes instead of overwriting the current version.
        This preserves the user's existing work while providing the latest updates.

        Args:
            request: Contains optional workflow_id, optional source_workflow_id, and user_id
            context: gRPC context for error handling

        Returns:
            Response with success status and updated workflow
        """
        db = self.get_db()
        logger.info(
            "pull_updates_from_source_request",
            workflow_id=getattr(request, 'workflow_id', None),
            source_workflow_id=getattr(request, 'source_workflow_id', None),
            user_id=request.user_id,
        )

        try:
            cloned_workflow = None
            source_workflow = None

            # Scenario 1: workflow_id provided - find the cloned workflow and its source
            if hasattr(request, 'workflow_id') and request.workflow_id:
                cloned_workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
                if not cloned_workflow:
                    context.set_code(grpc.StatusCode.NOT_FOUND)
                    context.set_details("Workflow not found")
                    return workflow_pb2.PullUpdatesFromSourceResponse(
                        success=False, message="Workflow not found"
                    )

                # Check ownership
                if cloned_workflow.owner_id != request.user_id:
                    context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                    context.set_details("Permission denied. You are not the owner of this workflow.")
                    return workflow_pb2.PullUpdatesFromSourceResponse(
                        success=False, message="Permission denied"
                    )

                # Check if this is a cloned workflow
                if not cloned_workflow.is_imported or not cloned_workflow.workflow_template_id:
                    context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                    context.set_details("This workflow is not cloned from a source workflow")
                    return workflow_pb2.PullUpdatesFromSourceResponse(
                        success=False, message="This workflow is not cloned from a source workflow"
                    )

                # Get the source workflow
                source_workflow = (
                    db.query(Workflow)
                    .filter(Workflow.id == cloned_workflow.workflow_template_id)
                    .first()
                )
                if not source_workflow:
                    context.set_code(grpc.StatusCode.NOT_FOUND)
                    context.set_details("Source workflow not found")
                    return workflow_pb2.PullUpdatesFromSourceResponse(
                        success=False, message="Source workflow not found"
                    )

            # Scenario 2: source_workflow_id provided - find the source workflow and relevant cloned workflow
            elif hasattr(request, 'source_workflow_id') and request.source_workflow_id:
                source_workflow = db.query(Workflow).filter(Workflow.id == request.source_workflow_id).first()
                if not source_workflow:
                    context.set_code(grpc.StatusCode.NOT_FOUND)
                    context.set_details("Source workflow not found")
                    return workflow_pb2.PullUpdatesFromSourceResponse(
                        success=False, message="Source workflow not found"
                    )

                # Find the user's cloned workflow from this source
                cloned_workflow = (
                    db.query(Workflow)
                    .filter(
                        Workflow.workflow_template_id == source_workflow.id,
                        Workflow.owner_id == request.user_id,
                        Workflow.is_imported == True
                    )
                    .first()
                )
                if not cloned_workflow:
                    context.set_code(grpc.StatusCode.NOT_FOUND)
                    context.set_details("No cloned workflow found for this source workflow and user")
                    return workflow_pb2.PullUpdatesFromSourceResponse(
                        success=False, message="No cloned workflow found for this source workflow and user"
                    )

            else:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Either workflow_id or source_workflow_id must be provided")
                return workflow_pb2.PullUpdatesFromSourceResponse(
                    success=False, message="Either workflow_id or source_workflow_id must be provided"
                )

            # Get the latest published version of the source workflow
            latest_source_version = None
            if source_workflow.current_version_id:
                latest_source_version = (
                    db.query(WorkflowVersion)
                    .filter(WorkflowVersion.id == source_workflow.current_version_id)
                    .first()
                )

            if not latest_source_version:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Latest version of source workflow not found")
                return workflow_pb2.PullUpdatesFromSourceResponse(
                    success=False, message="Latest version of source workflow not found"
                )

            # Check if already up to date
            if cloned_workflow.source_version_id == latest_source_version.id:
                return workflow_pb2.PullUpdatesFromSourceResponse(
                    success=True,
                    message=f"Workflow is already up to date with source version {latest_source_version.version_number}.",
                    updated_workflow=_workflow_to_protobuf(cloned_workflow, db),
                )

            # Get current version to determine next version number
            current_version = None
            if cloned_workflow.current_version_id:
                current_version = (
                    db.query(WorkflowVersion)
                    .filter(WorkflowVersion.id == cloned_workflow.current_version_id)
                    .first()
                )

            # Determine the next version number
            if current_version:
                # Parse current version and increment
                current_version_parts = current_version.version_number.split(".")
                if len(current_version_parts) == 3:
                    major, minor, patch = map(int, current_version_parts)
                    # Increment minor version for source updates
                    new_version_number = f"{major}.{minor + 1}.0"
                else:
                    # Fallback if version format is unexpected
                    new_version_number = "1.1.0"
            else:
                # No current version found, start with 1.1.0
                new_version_number = "1.1.0"

            # Create new version with source workflow changes
            current_time = datetime.now(timezone.utc)
            
            new_version = WorkflowVersion(
                workflow_id=cloned_workflow.id,
                version_number=new_version_number,
                name=latest_source_version.name,
                description=latest_source_version.description,
                image_url=getattr(latest_source_version, 'image_url', None),
                workflow_url=latest_source_version.workflow_url,
                builder_url=latest_source_version.builder_url,
                start_nodes=latest_source_version.start_nodes if latest_source_version.start_nodes else [],
                available_nodes=latest_source_version.available_nodes if latest_source_version.available_nodes else [],
                category=latest_source_version.category,
                tags=latest_source_version.tags if latest_source_version.tags else [],
                changelog=f"Synced with source workflow version {latest_source_version.version_number}",
                status=WorkflowStatusEnum.ACTIVE,
                is_customizable=cloned_workflow.is_customizable,
                created_at=current_time,
            )

            db.add(new_version)
            db.flush()  # Get the new version ID

            # Update the cloned workflow to use the new version and track source version
            cloned_workflow.current_version_id = new_version.id
            cloned_workflow.name = latest_source_version.name
            cloned_workflow.description = latest_source_version.description
            cloned_workflow.image_url = getattr(latest_source_version, 'image_url', None)
            cloned_workflow.workflow_url = latest_source_version.workflow_url
            cloned_workflow.builder_url = latest_source_version.builder_url
            cloned_workflow.start_nodes = latest_source_version.start_nodes
            cloned_workflow.available_nodes = latest_source_version.available_nodes
            cloned_workflow.category = latest_source_version.category
            cloned_workflow.tags = latest_source_version.tags
            
            # Update the source version tracking
            cloned_workflow.source_version_id = latest_source_version.id
            cloned_workflow.is_updated = False  # Reset the flag to indicate sync is complete
            cloned_workflow.updated_at = current_time

            db.add(cloned_workflow)
            db.commit()
            db.refresh(cloned_workflow)
            db.refresh(new_version)

            logger.info(
                f"Successfully created new version {new_version_number} for workflow {cloned_workflow.id} "
                f"with updates from source {source_workflow.id} version {latest_source_version.version_number} "
                f"(source version ID: {latest_source_version.id})"
            )

            return workflow_pb2.PullUpdatesFromSourceResponse(
                success=True,
                message=f"Successfully created version {new_version_number} with updates from source workflow version {latest_source_version.version_number}. Your previous work is preserved in earlier versions.",
                updated_workflow=_workflow_to_protobuf(cloned_workflow, db),
            )

        except Exception as e:
            db.rollback()
            logger.error("pull_updates_from_source_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.PullUpdatesFromSourceResponse(
                success=False, message=f"Failed to pull updates: {str(e)}"
            )
        finally:
            db.close()

    def checkForUpdates(
        self, request: workflow_pb2.CheckForUpdatesRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CheckForUpdatesResponse:
        """
        Check if a cloned workflow has updates available from its source workflow.

        Args:
            request: Contains workflow_id and user_id
            context: gRPC context for error handling

        Returns:
            Response with update availability information
        """
        db = self.get_db()
        logger.info(
            "check_for_updates_request", workflow_id=request.workflow_id, user_id=request.user_id
        )

        try:
            # Get the cloned workflow
            cloned_workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not cloned_workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found")
                return workflow_pb2.CheckForUpdatesResponse(
                    success=False, message="Workflow not found"
                )

            # Check ownership
            if cloned_workflow.owner_id != request.user_id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied. You are not the owner of this workflow.")
                return workflow_pb2.CheckForUpdatesResponse(
                    success=False, message="Permission denied"
                )

            # Check if this is a cloned workflow
            if not cloned_workflow.is_imported or not cloned_workflow.workflow_template_id:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("This workflow is not cloned from a source workflow")
                return workflow_pb2.CheckForUpdatesResponse(
                    success=False, message="This workflow is not cloned from a source workflow"
                )

            # Get the source workflow
            source_workflow = (
                db.query(Workflow)
                .filter(Workflow.id == cloned_workflow.workflow_template_id)
                .first()
            )
            if not source_workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Source workflow not found")
                return workflow_pb2.CheckForUpdatesResponse(
                    success=False, message="Source workflow not found"
                )

            # Enhanced update detection based on version comparison
            has_updates = False
            latest_version_id = ""
            latest_version_number = ""
            current_version_number = ""
            
            # Get the latest published version of the source workflow
            if source_workflow.current_version_id:
                latest_version_id = source_workflow.current_version_id
                
                # Get version details
                latest_version = (
                    db.query(WorkflowVersion)
                    .filter(WorkflowVersion.id == source_workflow.current_version_id)
                    .first()
                )
                
                if latest_version:
                    latest_version_number = latest_version.version_number
                
                # Check if cloned workflow is based on an older version
                if cloned_workflow.source_version_id:
                    # Get current version details
                    current_version = (
                        db.query(WorkflowVersion)
                        .filter(WorkflowVersion.id == cloned_workflow.source_version_id)
                        .first()
                    )
                    
                    if current_version:
                        current_version_number = current_version.version_number
                    
                    # Compare version IDs to detect updates
                    has_updates = cloned_workflow.source_version_id != source_workflow.current_version_id
                else:
                    # If no source_version_id, fall back to is_updated flag or assume updates available
                    has_updates = cloned_workflow.is_updated or True
            else:
                # Fall back to original logic if no current_version_id
                has_updates = cloned_workflow.is_updated or False

            message = (
                f"Updates available: new version {latest_version_number} (you have {current_version_number})"
                if has_updates and latest_version_number and current_version_number
                else "Updates available from source workflow"
                if has_updates
                else "Workflow is up to date"
            )

            logger.info(
                "check_for_updates_result",
                workflow_id=request.workflow_id,
                has_updates=has_updates,
                source_version_id=cloned_workflow.source_version_id,
                latest_version_id=latest_version_id,
                current_version=current_version_number,
                latest_version=latest_version_number,
            )

            return workflow_pb2.CheckForUpdatesResponse(
                success=True,
                message=message,
                has_updates=has_updates,
                source_workflow_id=cloned_workflow.workflow_template_id,
                last_updated=(
                    cloned_workflow.updated_at.isoformat() if cloned_workflow.updated_at else ""
                ),
                source_last_updated=(
                    source_workflow.updated_at.isoformat() if source_workflow.updated_at else ""
                ),
                current_source_version_id=latest_version_id,
                user_source_version_id=cloned_workflow.source_version_id,
                current_source_version_number=latest_version_number,
                user_source_version_number=current_version_number
            )

        except Exception as e:
            logger.error("check_for_updates_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.CheckForUpdatesResponse(
                success=False, message=f"Failed to check for updates: {str(e)}"
            )
        finally:
            db.close()
