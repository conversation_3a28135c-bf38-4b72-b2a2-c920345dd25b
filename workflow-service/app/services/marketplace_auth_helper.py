"""
Marketplace Authentication Helper
Provides authentication filtering for marketplace workflows using available_nodes data
"""

from sqlalchemy.orm import Query
from sqlalchemy import text, and_, or_
from typing import Optional, List
from app.models.workflow import WorkflowMarketplaceListing


class MarketplaceAuthHelper:
    """Helper class for filtering marketplace workflows by authentication requirements"""
    
    @staticmethod
    def apply_auth_filters(
        query: Query, 
        auth_complexity: Optional[str] = None,
        requires_auth: Optional[bool] = None, 
        provider_filter: Optional[str] = None,
        max_credentials: Optional[int] = None
    ) -> Query:
        """
        Apply authentication filters to marketplace workflow query
        
        Args:
            query: SQLAlchemy query for WorkflowMarketplaceListing
            auth_complexity: Filter by complexity (none, simple, moderate, complex)
            requires_auth: Filter by authentication requirement (True/False)
            provider_filter: Filter by specific provider (openai, github, etc.)
            max_credentials: Maximum number of credentials required
            
        Returns:
            Filtered query
        """
        
        # Get the source workflow for authentication data
        # We need to join with the Workflow table to access available_nodes
        from app.models.workflow import Workflow
        
        # Add join to access the source workflow's available_nodes
        query = query.join(Workflow, WorkflowMarketplaceListing.source_workflow_id == Workflow.id)
        
        # Filter by authentication complexity
        if auth_complexity:
            # Query available_nodes JSON for auth_summary node with matching complexity
            auth_complexity_filter = text("""
                EXISTS (
                    SELECT 1 FROM jsonb_array_elements(workflows.available_nodes) AS node
                    WHERE node->>'type' = 'auth_summary' 
                    AND node->'auth_data'->>'auth_complexity' = :complexity
                )
            """)
            query = query.filter(auth_complexity_filter.bindparam(complexity=auth_complexity))
        
        # Filter by authentication requirement
        if requires_auth is not None:
            if requires_auth:
                # Workflows that require authentication
                requires_auth_filter = text("""
                    EXISTS (
                        SELECT 1 FROM jsonb_array_elements(workflows.available_nodes) AS node
                        WHERE node->>'type' = 'auth_summary' 
                        AND (node->'auth_data'->>'requires_authentication')::boolean = true
                    )
                """)
            else:
                # Workflows that don't require authentication
                requires_auth_filter = text("""
                    NOT EXISTS (
                        SELECT 1 FROM jsonb_array_elements(workflows.available_nodes) AS node
                        WHERE node->>'type' = 'auth_summary' 
                        AND (node->'auth_data'->>'requires_authentication')::boolean = true
                    ) OR NOT EXISTS (
                        SELECT 1 FROM jsonb_array_elements(workflows.available_nodes) AS node
                        WHERE node->>'type' = 'auth_summary'
                    )
                """)
            
            query = query.filter(requires_auth_filter)
        
        # Filter by specific provider
        if provider_filter:
            provider_filter_condition = text("""
                EXISTS (
                    SELECT 1 FROM jsonb_array_elements(workflows.available_nodes) AS node
                    WHERE node->>'type' = 'auth_summary' 
                    AND node->'auth_data'->'oauth_providers_required' ? :provider
                )
            """)
            query = query.filter(provider_filter_condition.bindparam(provider=provider_filter))
        
        # Filter by maximum credentials
        if max_credentials is not None:
            max_creds_filter = text("""
                EXISTS (
                    SELECT 1 FROM jsonb_array_elements(workflows.available_nodes) AS node
                    WHERE node->>'type' = 'auth_summary' 
                    AND (node->'auth_data'->>'total_auth_items')::integer <= :max_creds
                ) OR NOT EXISTS (
                    SELECT 1 FROM jsonb_array_elements(workflows.available_nodes) AS node
                    WHERE node->>'type' = 'auth_summary'
                )
            """)
            query = query.filter(max_creds_filter.bindparam(max_creds=max_credentials))
        
        return query
    
    @staticmethod
    def extract_auth_summary_from_listing(listing) -> Optional[dict]:
        """
        Extract authentication summary from a marketplace listing
        
        Args:
            listing: WorkflowMarketplaceListing object with joined Workflow
            
        Returns:
            Authentication summary if found, None otherwise
        """
        if not hasattr(listing, 'workflow') or not listing.workflow:
            return None
            
        available_nodes = listing.workflow.available_nodes or []
        
        for node in available_nodes:
            if isinstance(node, dict) and node.get('type') == 'auth_summary':
                return node.get('auth_data')
        
        return None
    
    @staticmethod 
    def get_complexity_from_auth_summary(auth_summary: Optional[dict]) -> str:
        """Get complexity level from auth summary"""
        if not auth_summary:
            return "none"
        return auth_summary.get("auth_complexity", "none")
    
    @staticmethod
    def get_auth_info_for_response(listing) -> dict:
        """
        Get authentication info to include in API response
        
        Args:
            listing: WorkflowMarketplaceListing object
            
        Returns:
            Dictionary with auth info for API response
        """
        auth_summary = MarketplaceAuthHelper.extract_auth_summary_from_listing(listing)
        
        if not auth_summary:
            return {
                "requires_authentication": False,
                "auth_complexity": "none",
                "credential_count": 0,
                "required_providers": []
            }
        
        return {
            "requires_authentication": auth_summary.get("requires_authentication", False),
            "auth_complexity": auth_summary.get("auth_complexity", "none"), 
            "credential_count": auth_summary.get("total_auth_items", 0),
            "required_providers": auth_summary.get("oauth_providers_required", []),
            "required_env_keys": auth_summary.get("env_keys_required", [])
        }