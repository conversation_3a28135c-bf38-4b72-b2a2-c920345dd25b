import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  AlertCircle, 
  CheckCircle, 
  Shield, 
  Key,
  ExternalLink,
  Loader2
} from "lucide-react";
import { WorkflowAuthenticationSummary, getWorkflowAuthenticationRequirements } from "@/lib/api";

interface AuthenticationRequirementsCheckProps {
  workflowId: string;
  onProceed: () => void;
  onCancel: () => void;
}

export function AuthenticationRequirementsCheck({ 
  workflowId, 
  onProceed, 
  onCancel 
}: AuthenticationRequirementsCheckProps) {
  const [authSummary, setAuthSummary] = useState<WorkflowAuthenticationSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAuthRequirements = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const summary = await getWorkflowAuthenticationRequirements(workflowId);
        setAuthSummary(summary);
        
        // If no authentication required, proceed automatically
        if (!summary || !summary.requires_authentication) {
          console.log("No authentication requirements found, proceeding with execution");
          onProceed();
          return;
        }
        
      } catch (err) {
        console.error("Error checking authentication requirements:", err);
        setError(err instanceof Error ? err.message : "Failed to check authentication requirements");
      } finally {
        setLoading(false);
      }
    };

    if (workflowId) {
      checkAuthRequirements();
    }
  }, [workflowId, onProceed]);

  const getComplexityColor = (complexity: string) => {
    switch (complexity.toLowerCase()) {
      case 'none': return 'text-green-600';
      case 'simple': return 'text-blue-600';
      case 'moderate': return 'text-yellow-600';
      case 'complex': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getComplexityBadgeVariant = (complexity: string) => {
    switch (complexity.toLowerCase()) {
      case 'none': return 'default';
      case 'simple': return 'secondary';
      case 'moderate': return 'outline';
      case 'complex': return 'destructive';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
        <Card className="w-[500px] max-w-[90vw]">
          <CardContent className="p-6">
            <div className="flex items-center justify-center space-x-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Checking authentication requirements...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
        <Card className="w-[500px] max-w-[90vw]">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Authentication Check Failed
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">{error}</p>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button onClick={onProceed}>
                Proceed Anyway
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If no authentication required, this component shouldn't render
  // (onProceed would have been called automatically)
  if (!authSummary || !authSummary.requires_authentication) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
      <Card className="w-[600px] max-w-[90vw] max-h-[80vh] overflow-y-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Authentication Requirements
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Authentication Summary */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Authentication Complexity:</span>
              <Badge variant={getComplexityBadgeVariant(authSummary.auth_complexity)}>
                {authSummary.auth_complexity.toUpperCase()}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Total Requirements:</span>
              <span className="text-sm">{authSummary.total_auth_items}</span>
            </div>
          </div>

          {/* Environment Keys Required */}
          {authSummary.env_keys_required.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Key className="h-4 w-4" />
                Environment Variables Required ({authSummary.env_keys_required.length})
              </h4>
              <div className="grid grid-cols-1 gap-2">
                {authSummary.env_keys_required.map((key, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                    <code className="text-xs">{key}</code>
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* OAuth Providers Required */}
          {authSummary.oauth_providers_required.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">OAuth Providers Required ({authSummary.oauth_providers_required.length})</h4>
              <div className="grid grid-cols-1 gap-2">
                {authSummary.oauth_providers_required.map((provider, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                    <span className="text-xs capitalize">{provider}</span>
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Components with Authentication */}
          {authSummary.components_with_auth.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Components Requiring Authentication</h4>
              <div className="space-y-2">
                {authSummary.components_with_auth.map((component, index) => (
                  <div key={index} className="p-3 border rounded-lg space-y-2">
                    <div className="font-medium text-sm">{component.component_name}</div>
                    {component.env_keys.length > 0 && (
                      <div className="text-xs text-muted-foreground">
                        Environment Keys: {component.env_keys.join(", ")}
                      </div>
                    )}
                    {component.oauth_provider && (
                      <div className="text-xs text-muted-foreground">
                        OAuth Provider: {component.oauth_provider}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Warning Message */}
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-yellow-800">
                  Authentication Required
                </p>
                <p className="text-xs text-yellow-700">
                  This workflow requires authentication credentials to execute properly. 
                  Please ensure you have configured the required environment variables and OAuth connections 
                  before proceeding.
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button 
              onClick={onProceed}
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              Proceed with Execution
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
