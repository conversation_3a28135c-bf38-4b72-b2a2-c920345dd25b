'use client';
import React from 'react';
import AddVariableDialog from './AddVariableDialog';

interface Variable {
  key: string;
  value: string;
  secure: boolean;
}

export default function GlobalVariablePage() {
  const [showAddDialog, setShowAddDialog] = React.useState(false);
  const [variables, setVariables] = React.useState<Variable[]>([]);

  const handleAddVariable = (variable: Variable) => {
    setVariables((prev) => [...prev, variable]);
    setShowAddDialog(false);
  };

  return (
    <div className="flex flex-col min-h-screen w-full bg-black">
      {/* Top bar with search and sort */}
      <div className="flex items-center justify-between w-full px-4 pt-4">
        <div className="relative w-[260px]">
          <span className="absolute left-4 top-1/2 -translate-y-1/2 text-[#A1A1AA]">
            <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="8.5" cy="8.5" r="7.5" stroke="#A1A1AA" strokeWidth="2"/><path d="M16 16l-3-3" stroke="#A1A1AA" strokeWidth="2" strokeLinecap="round"/></svg>
          </span>
          <input
            type="text"
            placeholder="Search Global Variable...."
            className="w-full h-10 rounded-lg bg-[#232326] text-white font-[Satoshi] pl-10 pr-4 text-sm placeholder:text-[#A1A1AA] border border-[#393939] outline-none"
          />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-[#A1A1AA] font-[Satoshi] text-xs">Sort by:</span>
          <select className="h-10 rounded-lg bg-[#232326] text-white font-[Satoshi] px-4 text-xs border border-[#393939] outline-none">
            <option>Latest update</option>
            <option>Oldest update</option>
            <option>Name A-Z</option>
            <option>Name Z-A</option>
          </select>
        </div>
      </div>
      {/* Table of variables */}
      {variables.length > 0 && (
        <div className="w-full mt-8">
          <div className="w-[94%] mx-auto">
            <table className="w-full font-[Satoshi]">
              <thead>
                <tr className="bg-[#393940] text-white text-left text-[16px] font-bold">
                  <th className="py-4 px-6 rounded-tl-2xl">Key</th>
                  <th className="py-4 px-6">Value</th>
                  <th className="py-4 px-6">Usage syntax</th>
                  <th className="py-4 pr-8 rounded-tr-2xl text-right">More</th>
                </tr>
              </thead>
              <tbody className="bg-[#232326] text-white text-[15px]">
                {variables.map((v, i) => (
                  <tr key={i} className={i === variables.length - 1 ? 'rounded-b-2xl' : ''}>
                    <td className="py-4 px-6">{v.key}</td>
                    <td className="py-4 px-6 flex items-center gap-2">
                      {v.value}
                      {v.secure && <span className="inline-block w-2 h-2 rounded-full bg-[#A020F0]" />}
                    </td>
                    <td className="py-4 px-6">{'{{ $json.' + v.key + ' }}'}</td>
                    <td className="py-4 pr-8 text-right align-middle">
                      <div className="flex justify-end items-center h-full">
                        <button className="text-white text-2xl" style={{lineHeight: 1}}>&#8942;</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      {/* Centered content */}
      {variables.length === 0 && (
        <div className="flex flex-1 flex-col items-center justify-center w-full">
          <img src="/file.svg" alt="Workflow Icon" className="w-28 h-28 mb-8 opacity-60" />
          <h3 className="font-[Satoshi] text-white text-xl font-semibold mb-2">Create New variable</h3>
          <p className="font-[Satoshi] text-[#A1A1AA] text-base mb-4 text-center max-w-md">
          Global variables let you store values (like API keys, environment settings, or shared constants) that can be reused across all your workflows. Add your first global variable to get started!       </p>
          <button className="bg-[#A020F0] hover:bg-[#8B1FE0] text-white font-[Satoshi] font-medium rounded-lg px-8 py-3 text-base flex items-center gap-2 transition" onClick={() => setShowAddDialog(true)}>
            <span className="text-xl font-bold">+</span> Create new Variable
          </button>
        </div>
      )}
      <AddVariableDialog open={showAddDialog} onOpenChange={setShowAddDialog} onSave={handleAddVariable} />
    </div>
  );
} 