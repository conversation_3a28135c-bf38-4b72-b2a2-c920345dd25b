/**
 * Workflow Node Enhancer
 * 
 * This module provides functionality to reconcile existing workflow nodes
 * with enhanced component definitions, ensuring that loaded workflows
 * get the latest provider/model data instead of stale saved definitions.
 */

import { Node } from "reactflow";
import { ComponentsApiResponse, WorkflowNodeData } from "@/types";

/**
 * Check if a node is an AI component that needs enhancement
 */
function isAIComponent(node: Node<WorkflowNodeData>): boolean {
  // Check if it's in the AI category
  if (node.data.definition?.category === 'AI') return true;

  // Check if it has model_provider input (common pattern for AI components)
  if (node.data.definition?.inputs?.some((input: any) => input.name === 'model_provider')) return true;

  // Check node type patterns
  const nodeType = node.data.type || node.data.originalType || '';
  const aiComponentPatterns = [
    /agent/i,
    /chat/i,
    /llm/i,
    /ai/i,
    /gpt/i,
    /claude/i,
    /gemini/i,
    /mistral/i,
    /AgenticAI/i
  ];

  return aiComponentPatterns.some(pattern => pattern.test(nodeType));
}

/**
 * Find the enhanced component definition for a node
 */
function findEnhancedComponentDefinition(
  node: Node<WorkflowNodeData>, 
  enhancedComponents: ComponentsApiResponse
): any | null {
  const nodeType = node.data.type || node.data.originalType || '';
  
  // Search through all categories for matching component
  for (const [categoryName, categoryComponents] of Object.entries(enhancedComponents)) {
    for (const [componentName, componentDef] of Object.entries(categoryComponents)) {
      // Try multiple matching strategies
      if (
        componentName === nodeType ||
        nodeType.includes(componentName) ||
        componentName.includes(nodeType) ||
        // Special case for AgenticAI -> agent mapping
        (nodeType === 'AgenticAI' && componentName.toLowerCase().includes('agent')) ||
        (componentName === 'AgenticAI' && nodeType.toLowerCase().includes('agent'))
      ) {
        console.log(`🔄 Found enhanced definition for node ${node.id} (${nodeType}) -> ${categoryName}/${componentName}`);
        return componentDef;
      }
    }
  }

  return null;
}

/**
 * Enhance a single AI node's inputs with fresh provider/model data
 */
function enhanceNodeInputs(
  node: Node<WorkflowNodeData>, 
  enhancedComponentDef: any
): Node<WorkflowNodeData> {
  if (!node.data.definition?.inputs || !enhancedComponentDef?.inputs) {
    return node;
  }

  // Create a copy of the node to avoid mutations
  const enhancedNode = JSON.parse(JSON.stringify(node));

  // Create a map of enhanced inputs by name for quick lookup
  const enhancedInputsMap = new Map();
  enhancedComponentDef.inputs.forEach((input: any) => {
    enhancedInputsMap.set(input.name, input);
  });

  // Update existing inputs with enhanced data
  enhancedNode.data.definition.inputs = enhancedNode.data.definition.inputs.map((input: any) => {
    const enhancedInput = enhancedInputsMap.get(input.name);
    if (enhancedInput) {
      // Preserve existing value but update options and metadata
      const updatedInput = {
        ...enhancedInput, // Use enhanced definition as base
        value: input.value || enhancedInput.value // Preserve existing value if it exists
      };

      // Special handling for model_provider and model_name inputs
      if (input.name === 'model_provider' && enhancedInput.options) {
        console.log(`🔄 Updating provider options for node ${node.id}:`, enhancedInput.options);
      } else if (input.name === 'model_name') {
        console.log(`🔄 Updating model input with dynamic filtering for node ${node.id}`);
        // Ensure dynamic filtering metadata is preserved
        if (enhancedInput._dynamicFiltering) {
          updatedInput._dynamicFiltering = enhancedInput._dynamicFiltering;
          updatedInput._filterByField = enhancedInput._filterByField;
          updatedInput._providerIdMapping = enhancedInput._providerIdMapping;
        }
      }

      return updatedInput;
    }
    
    // Return original input if no enhanced version found
    return input;
  });

  // Add any new inputs from enhanced definition that don't exist in the node
  const existingInputNames = new Set(enhancedNode.data.definition.inputs.map((input: any) => input.name));
  enhancedComponentDef.inputs.forEach((enhancedInput: any) => {
    if (!existingInputNames.has(enhancedInput.name)) {
      console.log(`📝 Adding new input ${enhancedInput.name} to node ${node.id}`);
      enhancedNode.data.definition.inputs.push(enhancedInput);
    }
  });

  // Add enhanced metadata to the definition
  if (enhancedComponentDef._enhanced) {
    enhancedNode.data.definition._enhanced = true;
    enhancedNode.data.definition._enhancementTimestamp = Date.now();
    enhancedNode.data.definition._providerIdMapping = enhancedComponentDef._providerIdMapping;
    enhancedNode.data.definition._availableCredentials = enhancedComponentDef._availableCredentials;
  }

  console.log(`✅ Enhanced node ${node.id} with fresh provider/model data`);
  return enhancedNode;
}

/**
 * Reconcile existing workflow nodes with enhanced component definitions
 * 
 * This function updates existing nodes with fresh provider/model data while
 * preserving their configuration and connections.
 */
export function reconcileNodesWithEnhancedComponents(
  nodes: Node<WorkflowNodeData>[],
  enhancedComponents: ComponentsApiResponse
): Node<WorkflowNodeData>[] {
  console.log('🔄 Starting node reconciliation with enhanced components');
  console.log(`Processing ${nodes.length} nodes with ${Object.keys(enhancedComponents).length} component categories`);

  let enhancedCount = 0;
  
  const reconciledNodes = nodes.map(node => {
    // Only process AI components
    if (!isAIComponent(node)) {
      return node;
    }

    console.log(`🔍 Processing AI node: ${node.id} (type: ${node.data.type || node.data.originalType})`);

    // Find enhanced component definition
    const enhancedComponentDef = findEnhancedComponentDefinition(node, enhancedComponents);
    
    if (!enhancedComponentDef) {
      console.warn(`⚠️ No enhanced component definition found for node ${node.id}`);
      return node;
    }

    // Enhance the node with fresh data
    const enhancedNode = enhanceNodeInputs(node, enhancedComponentDef);
    enhancedCount++;
    
    return enhancedNode;
  });

  console.log(`✅ Node reconciliation complete: Enhanced ${enhancedCount} AI nodes out of ${nodes.length} total nodes`);
  
  return reconciledNodes;
}

/**
 * Check if nodes need reconciliation by comparing with enhanced components
 */
export function checkIfNodesNeedReconciliation(
  nodes: Node<WorkflowNodeData>[],
  enhancedComponents: ComponentsApiResponse
): boolean {
  const aiNodes = nodes.filter(isAIComponent);
  
  for (const node of aiNodes) {
    const enhancedDef = findEnhancedComponentDefinition(node, enhancedComponents);
    if (enhancedDef) {
      // Check if the node's definition is outdated
      const hasProviderInput = node.data.definition?.inputs?.find((input: any) => input.name === 'model_provider');
      const enhancedProviderInput = enhancedDef.inputs?.find((input: any) => input.name === 'model_provider');
      
      if (hasProviderInput && enhancedProviderInput) {
        // Compare options arrays
        const currentOptions = hasProviderInput.options || [];
        const enhancedOptions = enhancedProviderInput.options || [];
        
        // If options are different, reconciliation is needed
        if (JSON.stringify(currentOptions.sort()) !== JSON.stringify(enhancedOptions.sort())) {
          console.log(`🔍 Node ${node.id} needs reconciliation: Provider options differ`);
          return true;
        }
      }
    }
  }
  
  return false;
}