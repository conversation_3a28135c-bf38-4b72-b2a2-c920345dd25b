{"nodes": [{"id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "create_meta_campaign", "input_schema": {"predefined_fields": [{"field_name": "name", "data_type": {"type": "string", "description": "Campaign name"}, "required": true}, {"field_name": "objective", "data_type": {"type": "string", "description": "Campaign objective"}, "required": true}, {"field_name": "status", "data_type": {"type": "string", "description": "Campaign status"}, "required": false}, {"field_name": "buying_type", "data_type": {"type": "string", "description": "Buying type for the campaign"}, "required": false}, {"field_name": "bid_strategy", "data_type": {"type": "string", "description": "Bidding strategy"}, "required": false}, {"field_name": "daily_budget", "data_type": {"type": "string", "description": "Daily budget in cents"}, "required": false}, {"field_name": "lifetime_budget", "data_type": {"type": "string", "description": "Lifetime budget in cents"}, "required": false}, {"field_name": "start_time", "data_type": {"type": "string", "description": "Campaign start time (ISO format)"}, "required": false}, {"field_name": "end_time", "data_type": {"type": "string", "description": "Campaign end time (ISO format)"}, "required": false}, {"field_name": "special_ad_categories", "data_type": {"type": "string", "description": "Special ad categories (required by Meta API)", "items": {"type": "string"}}, "required": false}]}, "output_schema": {"predefined_fields": []}}]}, {"id": "CombineTextComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "CombineTextComponent", "input_schema": {"predefined_fields": [{"field_name": "main_input", "data_type": {"type": "string", "description": "The main text or list to combine. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "num_additional_inputs", "data_type": {"type": "number", "description": "Set the number of additional text inputs to show (1-10)."}, "required": false}, {"field_name": "separator", "data_type": {"type": "string", "description": "The character or string to join the text with. Leave blank for direct combining. Use __SPACE__, __TAB__, __NEWLINE__ for special characters."}, "required": false}, {"field_name": "input_1", "data_type": {"type": "string", "description": "Text for input 1. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_2", "data_type": {"type": "string", "description": "Text for input 2. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_3", "data_type": {"type": "string", "description": "Text for input 3. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_4", "data_type": {"type": "string", "description": "Text for input 4. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_5", "data_type": {"type": "string", "description": "Text for input 5. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_6", "data_type": {"type": "string", "description": "Text for input 6. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_7", "data_type": {"type": "string", "description": "Text for input 7. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_8", "data_type": {"type": "string", "description": "Text for input 8. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_9", "data_type": {"type": "string", "description": "Text for input 9. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_10", "data_type": {"type": "string", "description": "Text for input 10. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "result", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}, {"id": "MergeDataComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "MergeDataComponent", "input_schema": {"predefined_fields": [{"field_name": "main_input", "data_type": {"type": "object", "description": "The main data structure to merge. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "num_additional_inputs", "data_type": {"type": "number", "description": "Set the number of additional inputs to show (1-10)."}, "required": false}, {"field_name": "merge_strategy", "data_type": {"type": "string", "description": "How to handle conflicts when merging dictionaries. 'Aggregate' will combine conflicting values into a list. 'Structured Compose' creates custom key-value pairs."}, "required": false}, {"field_name": "output_key_1", "data_type": {"type": "string", "description": "Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_2", "data_type": {"type": "string", "description": "Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_3", "data_type": {"type": "string", "description": "Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_4", "data_type": {"type": "string", "description": "Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_5", "data_type": {"type": "string", "description": "Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_6", "data_type": {"type": "string", "description": "Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_7", "data_type": {"type": "string", "description": "Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_8", "data_type": {"type": "string", "description": "Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_9", "data_type": {"type": "string", "description": "Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_10", "data_type": {"type": "string", "description": "Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_11", "data_type": {"type": "string", "description": "Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "input_1", "data_type": {"type": "object", "description": "Data structure 1 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_2", "data_type": {"type": "object", "description": "Data structure 2 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_3", "data_type": {"type": "object", "description": "Data structure 3 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_4", "data_type": {"type": "object", "description": "Data structure 4 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_5", "data_type": {"type": "object", "description": "Data structure 5 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_6", "data_type": {"type": "object", "description": "Data structure 6 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_7", "data_type": {"type": "object", "description": "Data structure 7 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_8", "data_type": {"type": "object", "description": "Data structure 8 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_9", "data_type": {"type": "object", "description": "Data structure 9 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_10", "data_type": {"type": "object", "description": "Data structure 10 to merge. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output_data", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}, {"id": "LoopNode", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "LoopNode", "input_schema": {"predefined_fields": [{"field_name": "source_type", "data_type": {"type": "string", "description": "Choose whether to iterate over a list of items or a number range."}, "required": false}, {"field_name": "iteration_list", "data_type": {"type": "array", "description": "The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array."}, "required": false}, {"field_name": "batch_size", "data_type": {"type": "string", "description": "Number of items to process together in each iteration. For example, batch size 2 will process items in pairs: [item1, item2], [item3, item4], etc."}, "required": false}, {"field_name": "start", "data_type": {"type": "string", "description": "Starting number for the range. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "end", "data_type": {"type": "string", "description": "Ending number for the range (inclusive). Can be connected from another node or entered directly."}, "required": false}, {"field_name": "step", "data_type": {"type": "string", "description": "Step size for the range (default: 1). Can be connected from another node or entered directly."}, "required": false}, {"field_name": "parallel_execution", "data_type": {"type": "boolean", "description": "Execute loop iterations in parallel for better performance."}, "required": false}, {"field_name": "max_concurrent", "data_type": {"type": "number", "description": "Maximum number of iterations to run concurrently (1-20)."}, "required": false}, {"field_name": "preserve_order", "data_type": {"type": "boolean", "description": "Maintain the original order of items in the results."}, "required": false}, {"field_name": "iteration_timeout", "data_type": {"type": "number", "description": "Maximum time to wait for each iteration to complete (1-3600 seconds)."}, "required": false}, {"field_name": "aggregation_type", "data_type": {"type": "string", "description": "How to aggregate results from all iterations."}, "required": false}, {"field_name": "include_metadata", "data_type": {"type": "boolean", "description": "Include metadata (timing, iteration index, etc.) in results."}, "required": false}, {"field_name": "on_iteration_error", "data_type": {"type": "string", "description": "How to handle errors in individual iterations."}, "required": false}, {"field_name": "include_errors", "data_type": {"type": "boolean", "description": "Include error information in the final results."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "current_item", "data_type": {"type": "object", "description": "", "format": "string"}}, {"field_name": "final_results", "data_type": {"type": "array", "description": "", "format": "string"}}]}}]}, {"id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "create_spreadsheet", "input_schema": {"predefined_fields": [{"field_name": "title", "data_type": {"type": "string", "description": ""}, "required": true}, {"field_name": "worksheet_titles", "data_type": {"type": "string", "description": ""}, "required": false}]}, "output_schema": {"predefined_fields": []}}]}], "transitions": [{"id": "transition-LoopNode-1752847240364", "sequence": 1, "transition_type": "initial", "execution_type": "loop", "node_label": "For Each Loop", "node_info": {"node_id": "LoopNode", "tools_to_use": [{"tool_id": 1, "tool_name": "LoopNode", "tool_params": {"items": [{"field_name": "source_type", "data_type": "string", "field_value": "iteration_list"}, {"field_name": "iteration_list", "data_type": "array", "field_value": []}, {"field_name": "batch_size", "data_type": "string", "field_value": "1"}, {"field_name": "start", "data_type": "string", "field_value": "1"}, {"field_name": "end", "data_type": "string", "field_value": "10"}, {"field_name": "step", "data_type": "string", "field_value": "1"}, {"field_name": "parallel_execution", "data_type": "boolean", "field_value": true}, {"field_name": "max_concurrent", "data_type": "number", "field_value": 3}, {"field_name": "preserve_order", "data_type": "boolean", "field_value": true}, {"field_name": "iteration_timeout", "data_type": "number", "field_value": 60}, {"field_name": "aggregation_type", "data_type": "string", "field_value": "collect_all"}, {"field_name": "include_metadata", "data_type": "boolean", "field_value": true}, {"field_name": "on_iteration_error", "data_type": "string", "field_value": "continue"}, {"field_name": "include_errors", "data_type": "boolean", "field_value": true}]}}], "input_data": [], "output_data": []}, "result_resolution": {"node_type": "loop", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "iteration_list", "handle_name": "Iteration List", "data_type": "array", "required": false, "description": "The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array."}, {"handle_id": "batch_size", "handle_name": "<PERSON><PERSON> Si<PERSON>", "data_type": "string", "required": false, "description": "Number of items to process together in each iteration. For example, batch size 2 will process items in pairs: [item1, item2], [item3, item4], etc."}, {"handle_id": "start", "handle_name": "Start Number", "data_type": "string", "required": false, "description": "Starting number for the range. Can be connected from another node or entered directly."}, {"handle_id": "end", "handle_name": "End Number", "data_type": "string", "required": false, "description": "Ending number for the range (inclusive). Can be connected from another node or entered directly."}, {"handle_id": "step", "handle_name": "Step Size", "data_type": "string", "required": false, "description": "Step size for the range (default: 1). Can be connected from another node or entered directly."}], "output_handles": [{"handle_id": "current_item", "handle_name": "Current Item (Iteration Output)", "data_type": "object", "description": "The current item being processed in the loop iteration"}, {"handle_id": "final_results", "handle_name": "All Results (Exit Output)", "data_type": "array", "description": "Aggregated results from all loop iterations"}]}, "result_path_hints": {"current_item": "current_item", "final_results": "final_results"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.current_item", "output_data.current_item", "response.current_item", "data.current_item", "result.final_results", "output_data.final_results", "response.final_results", "data.final_results", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "current_item"}}, "approval_required": false, "end": true, "loop_config": {"iteration_behavior": "independent", "iteration_source": {"iteration_list": [], "batch_size": 1}, "exit_condition": {"condition_type": "all_items_processed"}, "iteration_settings": {"parallel_execution": true, "max_concurrent": 3, "preserve_order": true, "iteration_timeout": 60}, "result_aggregation": {"aggregation_type": "collect_all", "include_metadata": true}, "loop_body_configuration": {"entry_transitions": [], "exit_transitions": [], "chain_completion_detection": "auto_detect_chain_end", "chain_execution_timeout": 300, "chain_monitoring": {"enable_progress_tracking": true, "transition_completion_callbacks": true, "state_persistence": false}, "auto_detection_config": {"enable_auto_detection": true, "detection_strategy": "hybrid"}}, "error_handling": {"on_iteration_error": "continue", "include_errors": true}}}, {"id": "transition-l_SzzOcidXyBpzOYR3RWq", "sequence": 2, "transition_type": "initial", "execution_type": "MCP", "node_label": "Create Campaign", "node_info": {"node_id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "tools_to_use": [{"tool_id": 1, "tool_name": "create_meta_campaign", "tool_params": {"items": [{"field_name": "name", "data_type": "string", "field_value": "Solar Power Bank 20,000mAh"}, {"field_name": "objective", "data_type": "string", "field_value": "OUTCOME_AWARENESS"}, {"field_name": "status", "data_type": "string", "field_value": "PAUSED"}, {"field_name": "buying_type", "data_type": "string", "field_value": "AUCTION"}, {"field_name": "bid_strategy", "data_type": "string", "field_value": "LOWEST_COST_WITHOUT_CAP"}, {"field_name": "daily_budget", "data_type": "string", "field_value": "1000"}, {"field_name": "lifetime_budget", "data_type": "string", "field_value": ""}, {"field_name": "start_time", "data_type": "string", "field_value": null}, {"field_name": "end_time", "data_type": "string", "field_value": null}, {"field_name": "special_ad_categories", "data_type": "string", "field_value": ""}]}}], "input_data": [], "output_data": [{"to_transition_id": "transition-CombineTextComponent-*************", "target_node_id": "Combine Text", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "result", "result_path": "result", "edge_id": "reactflow__edge-l_SzzOcidXyBpzOYR3RWqresult-CombineTextComponent-*************main_input"}]}}]}, "result_resolution": {"node_type": "mcp", "expected_result_structure": "dynamic", "handle_registry": {"input_handles": [{"handle_id": "name", "handle_name": "Name", "data_type": "string", "required": true, "description": "Campaign name"}, {"handle_id": "objective", "handle_name": "objective", "data_type": "string", "required": true, "description": "Campaign objective"}, {"handle_id": "status", "handle_name": "status", "data_type": "string", "required": false, "description": "Campaign status"}, {"handle_id": "buying_type", "handle_name": "Buying Type", "data_type": "string", "required": false, "description": "Buying type for the campaign"}, {"handle_id": "bid_strategy", "handle_name": "bid strategy", "data_type": "string", "required": false, "description": "Bidding strategy"}, {"handle_id": "daily_budget", "handle_name": "Daily Budget", "data_type": "string", "required": false, "description": "Daily budget in cents"}, {"handle_id": "lifetime_budget", "handle_name": "Lifetime Budget", "data_type": "string", "required": false, "description": "Lifetime budget in cents"}, {"handle_id": "start_time", "handle_name": "Start Time", "data_type": "string", "required": false, "description": "Campaign start time (ISO format)"}, {"handle_id": "end_time", "handle_name": "End Time", "data_type": "string", "required": false, "description": "Campaign end time (ISO format)"}, {"handle_id": "special_ad_categories", "handle_name": "Special Ad Categories", "data_type": "string", "required": false, "description": "Special ad categories (required by Meta API)"}], "output_handles": [{"handle_id": "result", "handle_name": "Result", "data_type": "string", "description": ""}]}, "result_path_hints": {"result": "result"}, "dynamic_discovery": {"enabled": true, "fallback_patterns": ["result.result", "output_data.result", "response.result", "data.result", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": false, "supports_nested_results": true, "requires_dynamic_discovery": true, "primary_output_handle": "result"}}, "approval_required": false, "end": false}, {"id": "transition-MCP_Google_Sheets_create_spreadsheet-1752847940941", "sequence": 3, "transition_type": "initial", "execution_type": "MCP", "node_label": "Google Sheets - create_spreadsheet", "node_info": {"node_id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "tools_to_use": [{"tool_id": 1, "tool_name": "create_spreadsheet", "tool_params": {"items": [{"field_name": "title", "data_type": "string", "field_value": null}, {"field_name": "worksheet_titles", "data_type": "string", "field_value": null}]}}], "input_data": [], "output_data": []}, "result_resolution": {"node_type": "mcp", "expected_result_structure": "dynamic", "handle_registry": {"input_handles": [{"handle_id": "title", "handle_name": "Title", "data_type": "string", "required": true, "description": ""}, {"handle_id": "worksheet_titles", "handle_name": "Worksheet Titles", "data_type": "string", "required": false, "description": ""}], "output_handles": [{"handle_id": "result", "handle_name": "Result", "data_type": "string", "description": ""}]}, "result_path_hints": {"result": "result"}, "dynamic_discovery": {"enabled": true, "fallback_patterns": ["result.result", "output_data.result", "response.result", "data.result", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": false, "supports_nested_results": true, "requires_dynamic_discovery": true, "primary_output_handle": "result"}}, "approval_required": false, "end": true}, {"id": "transition-MergeDataComponent-1752847205951", "sequence": 4, "transition_type": "initial", "execution_type": "Components", "node_label": "Merge Data", "node_info": {"node_id": "MergeDataComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "MergeDataComponent", "tool_params": {"items": [{"field_name": "main_input", "data_type": "object", "field_value": {}}, {"field_name": "num_additional_inputs", "data_type": "number", "field_value": 0}, {"field_name": "merge_strategy", "data_type": "string", "field_value": "Overwrite"}, {"field_name": "output_key_1", "data_type": "string", "field_value": ""}, {"field_name": "output_key_2", "data_type": "string", "field_value": ""}, {"field_name": "output_key_3", "data_type": "string", "field_value": ""}, {"field_name": "output_key_4", "data_type": "string", "field_value": ""}, {"field_name": "output_key_5", "data_type": "string", "field_value": ""}, {"field_name": "output_key_6", "data_type": "string", "field_value": ""}, {"field_name": "output_key_7", "data_type": "string", "field_value": ""}, {"field_name": "output_key_8", "data_type": "string", "field_value": ""}, {"field_name": "output_key_9", "data_type": "string", "field_value": ""}, {"field_name": "output_key_10", "data_type": "string", "field_value": ""}, {"field_name": "output_key_11", "data_type": "string", "field_value": ""}, {"field_name": "input_1", "data_type": "object", "field_value": {}}, {"field_name": "input_2", "data_type": "object", "field_value": {}}, {"field_name": "input_3", "data_type": "object", "field_value": {}}, {"field_name": "input_4", "data_type": "object", "field_value": {}}, {"field_name": "input_5", "data_type": "object", "field_value": {}}, {"field_name": "input_6", "data_type": "object", "field_value": {}}, {"field_name": "input_7", "data_type": "object", "field_value": {}}, {"field_name": "input_8", "data_type": "object", "field_value": {}}, {"field_name": "input_9", "data_type": "object", "field_value": {}}, {"field_name": "input_10", "data_type": "object", "field_value": {}}]}}], "input_data": [], "output_data": []}, "result_resolution": {"node_type": "component", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "main_input", "handle_name": "Main Input", "data_type": "object", "required": true, "description": "The main data structure to merge. Can be connected from another node or entered directly."}, {"handle_id": "output_key_1", "handle_name": "Output Key 1", "data_type": "string", "required": false, "description": "Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_2", "handle_name": "Output Key 2", "data_type": "string", "required": false, "description": "Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_3", "handle_name": "Output Key 3", "data_type": "string", "required": false, "description": "Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_4", "handle_name": "Output Key 4", "data_type": "string", "required": false, "description": "Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_5", "handle_name": "Output Key 5", "data_type": "string", "required": false, "description": "Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_6", "handle_name": "Output Key 6", "data_type": "string", "required": false, "description": "Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_7", "handle_name": "Output Key 7", "data_type": "string", "required": false, "description": "Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_8", "handle_name": "Output Key 8", "data_type": "string", "required": false, "description": "Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_9", "handle_name": "Output Key 9", "data_type": "string", "required": false, "description": "Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_10", "handle_name": "Output Key 10", "data_type": "string", "required": false, "description": "Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_11", "handle_name": "Output Key 11", "data_type": "string", "required": false, "description": "Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy."}, {"handle_id": "input_1", "handle_name": "Input 1", "data_type": "object", "required": false, "description": "Data structure 1 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_2", "handle_name": "Input 2", "data_type": "object", "required": false, "description": "Data structure 2 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_3", "handle_name": "Input 3", "data_type": "object", "required": false, "description": "Data structure 3 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_4", "handle_name": "Input 4", "data_type": "object", "required": false, "description": "Data structure 4 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_5", "handle_name": "Input 5", "data_type": "object", "required": false, "description": "Data structure 5 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_6", "handle_name": "Input 6", "data_type": "object", "required": false, "description": "Data structure 6 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_7", "handle_name": "Input 7", "data_type": "object", "required": false, "description": "Data structure 7 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_8", "handle_name": "Input 8", "data_type": "object", "required": false, "description": "Data structure 8 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_9", "handle_name": "Input 9", "data_type": "object", "required": false, "description": "Data structure 9 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_10", "handle_name": "Input 10", "data_type": "object", "required": false, "description": "Data structure 10 to merge. Can be connected from another node or entered directly."}], "output_handles": [{"handle_id": "output_data", "handle_name": "Merged Data", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"output_data": "output_data", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.output_data", "output_data.output_data", "response.output_data", "data.output_data", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "output_data"}}, "approval_required": false, "end": true}, {"id": "transition-CombineTextComponent-*************", "sequence": 5, "transition_type": "standard", "execution_type": "Components", "node_label": "Combine Text", "node_info": {"node_id": "CombineTextComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "CombineTextComponent", "tool_params": {"items": [{"field_name": "main_input", "data_type": "string", "field_value": null}, {"field_name": "num_additional_inputs", "data_type": "number", "field_value": 0}, {"field_name": "separator", "data_type": "string", "field_value": ""}, {"field_name": "input_1", "data_type": "string", "field_value": ""}, {"field_name": "input_2", "data_type": "string", "field_value": ""}, {"field_name": "input_3", "data_type": "string", "field_value": ""}, {"field_name": "input_4", "data_type": "string", "field_value": ""}, {"field_name": "input_5", "data_type": "string", "field_value": ""}, {"field_name": "input_6", "data_type": "string", "field_value": ""}, {"field_name": "input_7", "data_type": "string", "field_value": ""}, {"field_name": "input_8", "data_type": "string", "field_value": ""}, {"field_name": "input_9", "data_type": "string", "field_value": ""}, {"field_name": "input_10", "data_type": "string", "field_value": ""}]}}], "input_data": [{"from_transition_id": "transition-l_SzzOcidXyBpzOYR3RWq", "source_node_id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-l_SzzOcidXyBpzOYR3RWq", "source_handle_id": "result", "target_handle_id": "main_input", "edge_id": "reactflow__edge-l_SzzOcidXyBpzOYR3RWqresult-CombineTextComponent-*************main_input"}]}], "output_data": []}, "result_resolution": {"node_type": "component", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "main_input", "handle_name": "Main Input", "data_type": "string", "required": false, "description": "The main text or list to combine. Can be connected from another node or entered directly."}, {"handle_id": "input_1", "handle_name": "Input 1", "data_type": "string", "required": false, "description": "Text for input 1. Can be connected from another node or entered directly."}, {"handle_id": "input_2", "handle_name": "Input 2", "data_type": "string", "required": false, "description": "Text for input 2. Can be connected from another node or entered directly."}, {"handle_id": "input_3", "handle_name": "Input 3", "data_type": "string", "required": false, "description": "Text for input 3. Can be connected from another node or entered directly."}, {"handle_id": "input_4", "handle_name": "Input 4", "data_type": "string", "required": false, "description": "Text for input 4. Can be connected from another node or entered directly."}, {"handle_id": "input_5", "handle_name": "Input 5", "data_type": "string", "required": false, "description": "Text for input 5. Can be connected from another node or entered directly."}, {"handle_id": "input_6", "handle_name": "Input 6", "data_type": "string", "required": false, "description": "Text for input 6. Can be connected from another node or entered directly."}, {"handle_id": "input_7", "handle_name": "Input 7", "data_type": "string", "required": false, "description": "Text for input 7. Can be connected from another node or entered directly."}, {"handle_id": "input_8", "handle_name": "Input 8", "data_type": "string", "required": false, "description": "Text for input 8. Can be connected from another node or entered directly."}, {"handle_id": "input_9", "handle_name": "Input 9", "data_type": "string", "required": false, "description": "Text for input 9. Can be connected from another node or entered directly."}, {"handle_id": "input_10", "handle_name": "Input 10", "data_type": "string", "required": false, "description": "Text for input 10. Can be connected from another node or entered directly."}], "output_handles": [{"handle_id": "result", "handle_name": "Combined Text", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"result": "result", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.result", "output_data.result", "response.result", "data.result", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "result"}}, "approval_required": false, "end": true}]}